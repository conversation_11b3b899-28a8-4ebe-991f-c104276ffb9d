interface Env {
  BUCKET: R2Bucket;
}

interface CompleteBody {
  parts: R2UploadedPart[];
}

abstract class BaseRequest {

  protected constructor(protected readonly key: string) {
  }

  abstract execute(): Promise<Response>;

  protected json(body: unknown) {
    return new Response(JSON.stringify(body));
  }

  protected error(message: string, status = 400) {
    return new Response(message, { status });
  }
}

class CreateRequest extends BaseRequest {

  constructor(private readonly bucket: R2Bucket, key: string) {
    super(key);
  }

  async execute(): Promise<Response> {
    const { key, uploadId } = await this.bucket.createMultipartUpload(this.key);
    return this.json({ key, uploadId });
  }
}

abstract class UploadRequest extends BaseRequest {
  protected readonly upload: R2MultipartUpload;

  constructor(bucket: R2Bucket, key: string, uploadId: string) {
    super(key);
    this.upload = bucket.resumeMultipartUpload(key, uploadId);
  }
}

class CompleteRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string, private readonly body: CompleteBody | null) {
    super(bucket, key, uploadId);
  }

  async execute(): Promise<Response> {
    if (this.body === null) {
      return this.error("Missing or incomplete body");
    }
    // Error handling in case the multipart upload does not exist anymore
    try {
      const object = await this.upload.complete(this.body.parts);
      return new Response(null, {
        headers: {
          etag: object.httpEtag
        }
      });
    } catch (error: any) {
      return this.error(error.message);
    }
  }
}

class UploadPartRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string, private readonly partNumber: string, private readonly body: Request['body']) {
    super(bucket, key, uploadId);
  }

  async execute(): Promise<Response> {
    if (this.body === null) {
      return this.error("Missing request body");
    }
    const partNumber = parseInt(this.partNumber);
    if (isNaN(partNumber)) {
      return this.error("Invalid part number");
    }
    try {
      const part = await this.upload.uploadPart(partNumber, this.body);
      return this.json(part);
    } catch (error: any) {
      return this.error(error.message);
    }
  }
}

class AbortRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string) {
    super(bucket, key, uploadId);
  }

  async execute(): Promise<Response> {
    try {
      await this.upload.abort();
    } catch (error: any) {
      return this.error(error.message);
    }
    return new Response(null, { status: 204 });
  }
}

export default {
  async fetch(request, env): Promise<Response> {
    const pathParts = new URL(request.url).pathname.slice(1).split('/');
    if (request.method === "POST") {
      if (pathParts.length === 1) {
        // POST /{key} - Create multipart upload
        return await new CreateRequest(env.BUCKET, pathParts[0]).execute();
      }
      if (pathParts.length == 2) {
        // POST /{key}/{uploadId} - Complete multipart upload
        const body = await request.json<CompleteBody>().catch(() => null);
        return await new CompleteRequest(env.BUCKET, pathParts[0], pathParts[1], body).execute();
      }
    }
    if (request.method === "PUT") {
      if (pathParts.length == 3) {
        // PUT /{key}/{uploadId}/{partNumber} - Upload a part
        return await new UploadPartRequest(env.BUCKET, pathParts[0], pathParts[1], pathParts[2], request.body).execute();
      }
    }
    if (request.method === "DELETE" && pathParts.length == 2) {
      // DELETE /{key}/{uploadId} - Abort multipart upload
      return await new AbortRequest(env.BUCKET, pathParts[0], pathParts[1]).execute();
    }
    return new Response("Not Found", { status: 404 });
  },
} satisfies ExportedHandler<Env>;