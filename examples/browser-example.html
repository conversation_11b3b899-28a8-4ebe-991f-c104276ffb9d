<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multipart Upload Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        input[type="file"] {
            padding: 8px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .file-info {
            background-color: #e2e3e5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .settings {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .settings h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .settings-row {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
        }
        
        .settings-row > div {
            flex: 1;
        }
        
        .settings input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Multipart Upload Demo</h1>
        
        <div class="settings">
            <h3>Upload Settings</h3>
            <div class="form-group">
                <label for="workerEndpoint">Worker Endpoint:</label>
                <input type="text" id="workerEndpoint" 
                       value="https://your-worker.your-subdomain.workers.dev" 
                       placeholder="https://your-worker.your-subdomain.workers.dev">
            </div>
            <div class="settings-row">
                <div>
                    <label for="partSize">Part Size (MB):</label>
                    <input type="number" id="partSize" value="5" min="1" max="100">
                </div>
                <div>
                    <label for="maxConcurrent">Max Concurrent Uploads:</label>
                    <input type="number" id="maxConcurrent" value="3" min="1" max="10">
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <label for="fileInput">Select File to Upload:</label>
            <input type="file" id="fileInput" accept="*/*">
        </div>
        
        <div class="form-group">
            <label for="objectKey">Object Key (path in bucket):</label>
            <input type="text" id="objectKey" placeholder="uploads/my-file.zip">
        </div>
        
        <button id="uploadBtn" onclick="startUpload()">Start Upload</button>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Preparing upload...</div>
        </div>
        
        <div class="result" id="result"></div>
    </div>

    <script src="javascript-client.js"></script>
    <script>
        let uploadClient;
        let isUploading = false;

        // Initialize client when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateClient();
            
            // Update client when settings change
            document.getElementById('workerEndpoint').addEventListener('change', updateClient);
            document.getElementById('partSize').addEventListener('change', updateClient);
            document.getElementById('maxConcurrent').addEventListener('change', updateClient);
            
            // Auto-generate object key when file is selected
            document.getElementById('fileInput').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file && !document.getElementById('objectKey').value) {
                    document.getElementById('objectKey').value = `uploads/${file.name}`;
                }
            });
        });

        function updateClient() {
            const endpoint = document.getElementById('workerEndpoint').value;
            const partSize = parseInt(document.getElementById('partSize').value) * 1024 * 1024;
            const maxConcurrent = parseInt(document.getElementById('maxConcurrent').value);
            
            uploadClient = new MultipartUploadClient(endpoint, {
                partSize: partSize,
                maxConcurrentUploads: maxConcurrent,
                retryAttempts: 3
            });
        }

        async function startUpload() {
            if (isUploading) return;
            
            const fileInput = document.getElementById('fileInput');
            const objectKey = document.getElementById('objectKey').value.trim();
            const file = fileInput.files[0];
            
            // Validation
            if (!file) {
                showResult('Please select a file to upload.', 'error');
                return;
            }
            
            if (!objectKey) {
                showResult('Please enter an object key.', 'error');
                return;
            }
            
            if (!uploadClient) {
                showResult('Please enter a valid worker endpoint.', 'error');
                return;
            }
            
            // Start upload
            isUploading = true;
            document.getElementById('uploadBtn').disabled = true;
            document.getElementById('uploadBtn').textContent = 'Uploading...';
            document.getElementById('progressContainer').style.display = 'block';
            hideResult();
            
            try {
                const result = await uploadClient.uploadFile(file, objectKey, {
                    onProgress: updateProgress,
                    metadata: {
                        'original-name': file.name,
                        'upload-timestamp': new Date().toISOString(),
                        'file-size': file.size.toString(),
                        'content-type': file.type || 'application/octet-stream'
                    }
                });
                
                showResult(`
                    <strong>✅ Upload Successful!</strong><br>
                    <div class="file-info">
                        Key: ${result.key}<br>
                        ETag: ${result.etag}<br>
                        Parts: ${result.totalParts}<br>
                        Size: ${formatBytes(result.totalSize)}<br>
                        Upload ID: ${result.uploadId}
                    </div>
                `, 'success');
                
            } catch (error) {
                console.error('Upload failed:', error);
                showResult(`❌ Upload failed: ${error.message}`, 'error');
            } finally {
                isUploading = false;
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('uploadBtn').textContent = 'Start Upload';
                document.getElementById('progressContainer').style.display = 'none';
            }
        }

        function updateProgress(progress) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = `${progress.percentage}%`;
            progressText.textContent = `${progress.percentage}% (${progress.completedParts}/${progress.totalParts} parts)`;
        }

        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }

        function hideResult() {
            document.getElementById('result').style.display = 'none';
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
