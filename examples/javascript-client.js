/**
 * JavaScript client example for the multipart upload Worker
 * 
 * This example demonstrates how to upload large files using the multipart upload API
 * with parallel part uploads for improved performance.
 */

class MultipartUploadClient {
  constructor(workerEndpoint, options = {}) {
    this.workerEndpoint = workerEndpoint.replace(/\/$/, ''); // Remove trailing slash
    this.partSize = options.partSize || 10 * 1024 * 1024; // Default 10MB parts
    this.maxConcurrentUploads = options.maxConcurrentUploads || 5;
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000; // 1 second
  }

  /**
   * Upload a file using multipart upload
   * @param {File|Blob} file - The file to upload
   * @param {string} key - The object key (path) in the bucket
   * @param {Object} options - Upload options
   * @returns {Promise<Object>} Upload result
   */
  async uploadFile(file, key, options = {}) {
    const { onProgress, metadata } = options;
    
    console.log(`Starting multipart upload for ${key} (${file.size} bytes)`);
    
    try {
      // Step 1: Create multipart upload
      const createResponse = await this.createMultipartUpload(key, metadata);
      const { uploadId } = createResponse;
      
      console.log(`Created multipart upload: ${uploadId}`);
      
      // Step 2: Calculate parts
      const totalParts = Math.ceil(file.size / this.partSize);
      const parts = [];
      
      console.log(`File will be uploaded in ${totalParts} parts`);
      
      // Step 3: Upload parts in parallel with concurrency control
      const uploadPromises = [];
      let completedParts = 0;
      
      for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
        const start = (partNumber - 1) * this.partSize;
        const end = Math.min(start + this.partSize, file.size);
        const partData = file.slice(start, end);
        
        const uploadPromise = this.uploadPartWithRetry(
          key, 
          uploadId, 
          partNumber, 
          partData
        ).then(result => {
          completedParts++;
          if (onProgress) {
            onProgress({
              completedParts,
              totalParts,
              percentage: Math.round((completedParts / totalParts) * 100)
            });
          }
          return result;
        });
        
        uploadPromises.push(uploadPromise);
        
        // Control concurrency
        if (uploadPromises.length >= this.maxConcurrentUploads) {
          const completedPart = await Promise.race(uploadPromises);
          parts.push(completedPart);
          uploadPromises.splice(uploadPromises.indexOf(completedPart), 1);
        }
      }
      
      // Wait for remaining uploads
      const remainingParts = await Promise.all(uploadPromises);
      parts.push(...remainingParts);
      
      // Sort parts by part number
      parts.sort((a, b) => a.partNumber - b.partNumber);
      
      console.log(`All ${parts.length} parts uploaded successfully`);
      
      // Step 4: Complete multipart upload
      const completeResponse = await this.completeMultipartUpload(key, uploadId, parts);
      
      console.log(`Multipart upload completed: ${completeResponse.etag}`);
      
      return {
        key: completeResponse.key,
        etag: completeResponse.etag,
        location: completeResponse.location,
        uploadId,
        totalParts: parts.length,
        totalSize: file.size
      };
      
    } catch (error) {
      console.error('Multipart upload failed:', error);
      
      // Attempt to abort the upload on failure
      if (uploadId) {
        try {
          await this.abortMultipartUpload(key, uploadId);
          console.log('Aborted failed multipart upload');
        } catch (abortError) {
          console.error('Failed to abort multipart upload:', abortError);
        }
      }
      
      throw error;
    }
  }

  /**
   * Create a new multipart upload
   */
  async createMultipartUpload(key, metadata = {}) {
    const url = `${this.workerEndpoint}/create/${key}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ metadata })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to create multipart upload: ${error.message}`);
    }

    return await response.json();
  }

  /**
   * Upload a single part with retry logic
   */
  async uploadPartWithRetry(key, uploadId, partNumber, partData) {
    let lastError;
    
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        return await this.uploadPart(key, uploadId, partNumber, partData);
      } catch (error) {
        lastError = error;
        console.warn(`Part ${partNumber} upload attempt ${attempt} failed:`, error.message);
        
        if (attempt < this.retryAttempts) {
          await this.delay(this.retryDelay * attempt); // Exponential backoff
        }
      }
    }
    
    throw lastError;
  }

  /**
   * Upload a single part
   */
  async uploadPart(key, uploadId, partNumber, partData) {
    const url = `${this.workerEndpoint}/uploadpart/${key}?uploadId=${uploadId}&partNumber=${partNumber}`;

    const response = await fetch(url, {
      method: 'PUT',
      body: partData
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to upload part ${partNumber}: ${error.message}`);
    }

    return await response.json();
  }

  /**
   * Complete the multipart upload
   */
  async completeMultipartUpload(key, uploadId, parts) {
    const url = `${this.workerEndpoint}/complete/${key}?uploadId=${uploadId}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ parts })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to complete multipart upload: ${error.message}`);
    }

    return await response.json();
  }

  /**
   * Abort a multipart upload
   */
  async abortMultipartUpload(key, uploadId) {
    const url = `${this.workerEndpoint}/abort/${key}?uploadId=${uploadId}`;

    const response = await fetch(url, {
      method: 'DELETE'
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to abort multipart upload: ${error.message}`);
    }
  }

  /**
   * Download an object
   */
  async downloadObject(key) {
    const url = `${this.workerEndpoint}/get/${key}`;

    const response = await fetch(url, {
      method: 'GET'
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to download object: ${error.message}`);
    }

    return response;
  }

  /**
   * Delete an object
   */
  async deleteObject(key) {
    const url = `${this.workerEndpoint}/delete/${key}`;

    const response = await fetch(url, {
      method: 'DELETE'
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to delete object: ${error.message}`);
    }
  }

  /**
   * Utility function for delays
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Example usage
async function example() {
  const client = new MultipartUploadClient('https://your-worker.your-subdomain.workers.dev', {
    partSize: 5 * 1024 * 1024, // 5MB parts
    maxConcurrentUploads: 3,
    retryAttempts: 3
  });

  // Get file from input element
  const fileInput = document.getElementById('fileInput');
  const file = fileInput.files[0];

  if (file) {
    try {
      const result = await client.uploadFile(file, `uploads/${file.name}`, {
        onProgress: (progress) => {
          console.log(`Upload progress: ${progress.percentage}% (${progress.completedParts}/${progress.totalParts} parts)`);
        },
        metadata: {
          'original-name': file.name,
          'upload-timestamp': new Date().toISOString()
        }
      });

      console.log('Upload completed:', result);
    } catch (error) {
      console.error('Upload failed:', error);
    }
  }
}

// Export for use in Node.js or modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MultipartUploadClient;
}
