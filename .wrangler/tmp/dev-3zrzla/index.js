// .wrangler/tmp/bundle-Oki8io/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// src/index.ts
var BaseRequest = class {
  constructor(key) {
    this.key = key;
  }
  json(body) {
    return new Response(JSON.stringify(body));
  }
  error(message, status = 400) {
    return new Response(message, { status });
  }
};
var CreateRequest = class extends BaseRequest {
  constructor(bucket, key) {
    super(key);
    this.bucket = bucket;
  }
  async execute() {
    const { key, uploadId } = await this.bucket.createMultipartUpload(this.key);
    return this.json({ key, uploadId });
  }
};
var UploadRequest = class extends BaseRequest {
  upload;
  constructor(bucket, key, uploadId) {
    super(key);
    this.upload = bucket.resumeMultipartUpload(key, uploadId);
  }
};
var CompleteRequest = class extends UploadRequest {
  constructor(bucket, key, uploadId, body) {
    super(bucket, key, uploadId);
    this.body = body;
  }
  async execute() {
    if (this.body === null) {
      return this.error("Missing or incomplete body");
    }
    try {
      const object = await this.upload.complete(this.body.parts);
      return new Response(null, {
        headers: {
          etag: object.httpEtag
        }
      });
    } catch (error) {
      return this.error(error.message);
    }
  }
};
var UploadPartRequest = class extends UploadRequest {
  constructor(bucket, key, uploadId, partNumber, body) {
    super(bucket, key, uploadId);
    this.partNumber = partNumber;
    this.body = body;
  }
  async execute() {
    if (this.body === null) {
      return this.error("Missing request body");
    }
    const partNumber = parseInt(this.partNumber);
    if (isNaN(partNumber)) {
      return this.error("Invalid part number");
    }
    try {
      const part = await this.upload.uploadPart(partNumber, this.body);
      return this.json(part);
    } catch (error) {
      return this.error(error.message);
    }
  }
};
var AbortRequest = class extends UploadRequest {
  constructor(bucket, key, uploadId) {
    super(bucket, key, uploadId);
  }
  async execute() {
    try {
      await this.upload.abort();
    } catch (error) {
      return this.error(error.message);
    }
    return new Response(null, { status: 204 });
  }
};
var src_default = {
  async fetch(request, env) {
    const pathParts = new URL(request.url).pathname.slice(1).split("/");
    if (request.method === "POST") {
      if (pathParts.length === 1) {
        return await new CreateRequest(env.BUCKET, pathParts[0]).execute();
      }
      if (pathParts.length == 2) {
        const body = await request.json().catch(() => null);
        return await new CompleteRequest(env.BUCKET, pathParts[0], pathParts[1], body).execute();
      }
    }
    if (request.method === "PUT") {
      if (pathParts.length == 3) {
        return await new UploadPartRequest(env.BUCKET, pathParts[0], pathParts[1], pathParts[2], request.body).execute();
      }
    }
    if (request.method === "DELETE" && pathParts.length == 2) {
      return await new AbortRequest(env.BUCKET, pathParts[0], pathParts[1]).execute();
    }
    return new Response("Not Found", { status: 404 });
  }
};

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
};
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
var jsonError = async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
};
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-Oki8io/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}

// .wrangler/tmp/bundle-Oki8io/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  };
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      };
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
