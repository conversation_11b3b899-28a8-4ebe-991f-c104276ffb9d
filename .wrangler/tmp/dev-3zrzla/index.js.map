{"version": 3, "sources": ["../bundle-Oki8io/checked-fetch.js", "../../../src/index.ts", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-Oki8io/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-Oki8io/middleware-loader.entry.ts"], "sourceRoot": "/Users/<USER>/Projects/api-upload/.wrangler/tmp/dev-3zrzla", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "interface Env {\n  BUCKET: R2Bucket;\n}\n\ninterface CompleteBody {\n  parts: R2UploadedPart[];\n}\n\nabstract class BaseRequest {\n\n  protected constructor(protected readonly key: string) {\n  }\n\n  abstract execute(): Promise<Response>;\n\n  protected json(body: unknown) {\n    return new Response(JSON.stringify(body));\n  }\n\n  protected error(message: string, status = 400) {\n    return new Response(message, { status });\n  }\n}\n\nclass CreateRequest extends BaseRequest {\n\n  constructor(private readonly bucket: R2Bucket, key: string) {\n    super(key);\n  }\n\n  async execute(): Promise<Response> {\n    const { key, uploadId } = await this.bucket.createMultipartUpload(this.key);\n    return this.json({ key, uploadId });\n  }\n}\n\nabstract class UploadRequest extends BaseRequest {\n  protected readonly upload: R2MultipartUpload;\n\n  constructor(bucket: R2Bucket, key: string, uploadId: string) {\n    super(key);\n    this.upload = bucket.resumeMultipartUpload(key, uploadId);\n  }\n}\n\nclass CompleteRequest extends UploadRequest {\n\n  constructor(bucket: R2Bucket, key: string, uploadId: string, private readonly body: CompleteBody | null) {\n    super(bucket, key, uploadId);\n  }\n\n  async execute(): Promise<Response> {\n    if (this.body === null) {\n      return this.error(\"Missing or incomplete body\");\n    }\n    // Error handling in case the multipart upload does not exist anymore\n    try {\n      const object = await this.upload.complete(this.body.parts);\n      return new Response(null, {\n        headers: {\n          etag: object.httpEtag\n        }\n      });\n    } catch (error: any) {\n      return this.error(error.message);\n    }\n  }\n}\n\nclass UploadPartRequest extends UploadRequest {\n\n  constructor(bucket: R2Bucket, key: string, uploadId: string, private readonly partNumber: string, private readonly body: Request['body']) {\n    super(bucket, key, uploadId);\n  }\n\n  async execute(): Promise<Response> {\n    if (this.body === null) {\n      return this.error(\"Missing request body\");\n    }\n    const partNumber = parseInt(this.partNumber);\n    if (isNaN(partNumber)) {\n      return this.error(\"Invalid part number\");\n    }\n    try {\n      const part = await this.upload.uploadPart(partNumber, this.body);\n      return this.json(part);\n    } catch (error: any) {\n      return this.error(error.message);\n    }\n  }\n}\n\nclass AbortRequest extends UploadRequest {\n\n  constructor(bucket: R2Bucket, key: string, uploadId: string) {\n    super(bucket, key, uploadId);\n  }\n\n  async execute(): Promise<Response> {\n    try {\n      await this.upload.abort();\n    } catch (error: any) {\n      return this.error(error.message);\n    }\n    return new Response(null, { status: 204 });\n  }\n}\n\nexport default {\n  async fetch(request, env): Promise<Response> {\n    const pathParts = new URL(request.url).pathname.slice(1).split('/');\n    if (request.method === \"POST\") {\n      if (pathParts.length === 1) {\n        // POST /{key} - Create multipart upload\n        return await new CreateRequest(env.BUCKET, pathParts[0]).execute();\n      }\n      if (pathParts.length == 2) {\n        // POST /{key}/{uploadId} - Complete multipart upload\n        const body = await request.json<CompleteBody>().catch(() => null);\n        return await new CompleteRequest(env.BUCKET, pathParts[0], pathParts[1], body).execute();\n      }\n    }\n    if (request.method === \"PUT\") {\n      if (pathParts.length == 3) {\n        // PUT /{key}/{uploadId}/{partNumber} - Upload a part\n        return await new UploadPartRequest(env.BUCKET, pathParts[0], pathParts[1], pathParts[2], request.body).execute();\n      }\n    }\n    if (request.method === \"DELETE\" && pathParts.length == 2) {\n      // DELETE /{key}/{uploadId} - Abort multipart upload\n      return await new AbortRequest(env.BUCKET, pathParts[0], pathParts[1]).execute();\n    }\n    return new Response(\"Not Found\", { status: 404 });\n  },\n} satisfies ExportedHandler<Env>;", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTH<PERSON>_EXPORTS from \"/Users/<USER>/Projects/api-upload/src/index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"/Users/<USER>/Projects/api-upload/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"/Users/<USER>/Projects/api-upload/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"/Users/<USER>/Projects/api-upload/src/index.ts\";\n\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"/Users/<USER>/Projects/api-upload/.wrangler/tmp/bundle-Oki8io/middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"/Users/<USER>/Projects/api-upload/node_modules/wrangler/templates/middleware/common.ts\";\nimport type { WorkerEntrypointConstructor } from \"/Users/<USER>/Projects/api-upload/.wrangler/tmp/bundle-Oki8io/middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"/Users/<USER>/Projects/api-upload/.wrangler/tmp/bundle-Oki8io/middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS;AAAA;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AACD;AAEA,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;ACrBD,IAAe,cAAf,MAA2B;AAAA,EAEf,YAA+B,KAAa;AAAb;AAAA,EACzC;AAAA,EAIU,KAAK,MAAe;AAC5B,WAAO,IAAI,SAAS,KAAK,UAAU,IAAI,CAAC;AAAA,EAC1C;AAAA,EAEU,MAAM,SAAiB,SAAS,KAAK;AAC7C,WAAO,IAAI,SAAS,SAAS,EAAE,OAAO,CAAC;AAAA,EACzC;AACF;AAEA,IAAM,gBAAN,cAA4B,YAAY;AAAA,EAEtC,YAA6B,QAAkB,KAAa;AAC1D,UAAM,GAAG;AADkB;AAAA,EAE7B;AAAA,EAEA,MAAM,UAA6B;AACjC,UAAM,EAAE,KAAK,SAAS,IAAI,MAAM,KAAK,OAAO,sBAAsB,KAAK,GAAG;AAC1E,WAAO,KAAK,KAAK,EAAE,KAAK,SAAS,CAAC;AAAA,EACpC;AACF;AAEA,IAAe,gBAAf,cAAqC,YAAY;AAAA,EAC5B;AAAA,EAEnB,YAAY,QAAkB,KAAa,UAAkB;AAC3D,UAAM,GAAG;AACT,SAAK,SAAS,OAAO,sBAAsB,KAAK,QAAQ;AAAA,EAC1D;AACF;AAEA,IAAM,kBAAN,cAA8B,cAAc;AAAA,EAE1C,YAAY,QAAkB,KAAa,UAAmC,MAA2B;AACvG,UAAM,QAAQ,KAAK,QAAQ;AADiD;AAAA,EAE9E;AAAA,EAEA,MAAM,UAA6B;AACjC,QAAI,KAAK,SAAS,MAAM;AACtB,aAAO,KAAK,MAAM,4BAA4B;AAAA,IAChD;AAEA,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,OAAO,SAAS,KAAK,KAAK,KAAK;AACzD,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,UACP,MAAM,OAAO;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH,SAAS,OAAP;AACA,aAAO,KAAK,MAAM,MAAM,OAAO;AAAA,IACjC;AAAA,EACF;AACF;AAEA,IAAM,oBAAN,cAAgC,cAAc;AAAA,EAE5C,YAAY,QAAkB,KAAa,UAAmC,YAAqC,MAAuB;AACxI,UAAM,QAAQ,KAAK,QAAQ;AADiD;AAAqC;AAAA,EAEnH;AAAA,EAEA,MAAM,UAA6B;AACjC,QAAI,KAAK,SAAS,MAAM;AACtB,aAAO,KAAK,MAAM,sBAAsB;AAAA,IAC1C;AACA,UAAM,aAAa,SAAS,KAAK,UAAU;AAC3C,QAAI,MAAM,UAAU,GAAG;AACrB,aAAO,KAAK,MAAM,qBAAqB;AAAA,IACzC;AACA,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,OAAO,WAAW,YAAY,KAAK,IAAI;AAC/D,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB,SAAS,OAAP;AACA,aAAO,KAAK,MAAM,MAAM,OAAO;AAAA,IACjC;AAAA,EACF;AACF;AAEA,IAAM,eAAN,cAA2B,cAAc;AAAA,EAEvC,YAAY,QAAkB,KAAa,UAAkB;AAC3D,UAAM,QAAQ,KAAK,QAAQ;AAAA,EAC7B;AAAA,EAEA,MAAM,UAA6B;AACjC,QAAI;AACF,YAAM,KAAK,OAAO,MAAM;AAAA,IAC1B,SAAS,OAAP;AACA,aAAO,KAAK,MAAM,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC3C;AACF;AAEA,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAwB;AAC3C,UAAM,YAAY,IAAI,IAAI,QAAQ,GAAG,EAAE,SAAS,MAAM,CAAC,EAAE,MAAM,GAAG;AAClE,QAAI,QAAQ,WAAW,QAAQ;AAC7B,UAAI,UAAU,WAAW,GAAG;AAE1B,eAAO,MAAM,IAAI,cAAc,IAAI,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ;AAAA,MACnE;AACA,UAAI,UAAU,UAAU,GAAG;AAEzB,cAAM,OAAO,MAAM,QAAQ,KAAmB,EAAE,MAAM,MAAM,IAAI;AAChE,eAAO,MAAM,IAAI,gBAAgB,IAAI,QAAQ,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,EAAE,QAAQ;AAAA,MACzF;AAAA,IACF;AACA,QAAI,QAAQ,WAAW,OAAO;AAC5B,UAAI,UAAU,UAAU,GAAG;AAEzB,eAAO,MAAM,IAAI,kBAAkB,IAAI,QAAQ,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,QAAQ,IAAI,EAAE,QAAQ;AAAA,MACjH;AAAA,IACF;AACA,QAAI,QAAQ,WAAW,YAAY,UAAU,UAAU,GAAG;AAExD,aAAO,MAAM,IAAI,aAAa,IAAI,QAAQ,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,QAAQ;AAAA,IAChF;AACA,WAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,EAClD;AACF;;;ACpIA,IAAM,YAAwB,OAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAP;AACD,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD;AAEA,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAGA,IAAM,YAAwB,OAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAP;AACD,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD;AAEA,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAOA,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAEO,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;;;ACtDA,IAAM,iCAAN,MAAoE;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EARS;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,iCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,SACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC;AAEA,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,SAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD;AACA,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAEA,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,CACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IAEA,cAA0B,CAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAEA,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}